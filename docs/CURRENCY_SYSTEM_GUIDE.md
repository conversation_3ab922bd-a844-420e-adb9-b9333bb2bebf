# 🎯 Centralized Currency System Guide

## Overview

The new centralized currency system allows you to **change the entire application's currency in ONE place** and have it automatically reflect everywhere throughout the application.

## 🚀 Quick Start - Change Currency

### To Switch Currency (e.g., from LKR to USD):

1. **Open**: `shared/config/currencyConfig.ts`
2. **Find**: `DEFAULT_CURRENCY_CODE` (around line 85)
3. **Change**: 
   ```typescript
   // FROM:
   export const DEFAULT_CURRENCY_CODE: keyof typeof AVAILABLE_CURRENCIES = 'LKR';
   
   // TO:
   export const DEFAULT_CURRENCY_CODE: keyof typeof AVAILABLE_CURRENCIES = 'USD';
   ```
4. **Save** - That's it! The entire app now uses USD.

### Available Currencies:
- `'LKR'` - Sri Lankan Rupee (with custom icons)
- `'USD'` - US Dollar
- `'EUR'` - Euro
- `'GBP'` - British Pound
- `'INR'` - Indian Rupee

## 🏗️ System Architecture

### 1. Configuration Layer (`shared/config/currencyConfig.ts`)
- **Single source of truth** for all currency settings
- Currency definitions with icons, symbols, formatting rules
- Context-specific icon variants (header=white, table=black, etc.)

### 2. Context Layer (`shared/contexts/CurrencyContext.tsx`)
- React Context that provides currency state globally
- Formatting functions that respect current currency settings
- Icon variant selection based on UI context

### 3. Hook Layer (`shared/hooks/useCurrency.ts`)
- Easy-to-use hook for components: `const { formatCurrency } = useCurrency()`
- Backward compatibility with legacy currency utilities
- Context-specific formatting helpers

### 4. Component Layer (`shared/UI/components/currency/`)
- **CurrencyIcon**: Smart icon that auto-selects correct variant
- **CurrencyDisplay**: Complete currency display (icon + amount)
- Automatic currency detection and formatting

## 📝 Usage Examples

### Basic Currency Display
```tsx
import { CurrencyDisplay } from '@/shared/UI/components';

// Automatically uses current currency with correct icon/symbol
<CurrencyDisplay 
  amount={1500.50} 
  context="table" 
/>
```

### Using the Hook
```tsx
import { useCurrency } from '@/shared/hooks/useCurrency';

const MyComponent = () => {
  const { formatCurrency, hasCustomIcon, getIconVariant } = useCurrency();
  
  return (
    <div>
      <span>{formatCurrency(1500.50)}</span>
      {hasCustomIcon && <span>Uses custom icons!</span>}
    </div>
  );
};
```

### Context-Aware Icons
```tsx
import { CurrencyIcon } from '@/shared/UI/components';

// Automatically uses white icon for headers, black for tables
<CurrencyIcon context="header" size={20} />  // White variant
<CurrencyIcon context="table" size={16} />   // Black variant
```

## 🔧 Adding New Currencies

### 1. Add Currency Configuration
In `shared/config/currencyConfig.ts`, add to `AVAILABLE_CURRENCIES`:

```typescript
NEWCURRENCY: {
  code: 'NEW',
  name: 'New Currency',
  symbol: '₦',
  iconVariant: 'black',
  hasCustomIcon: false, // true if you have custom SVG icons
  decimalPlaces: 2,
  locale: 'en-US',
  symbolPosition: 'before',
  thousandsSeparator: ',',
  decimalSeparator: '.'
}
```

### 2. Add Custom Icons (Optional)
If `hasCustomIcon: true`, add SVG files to `public/assets/currency/`:
- `chips_black.svg`
- `chips_red.svg` 
- `chips_white.svg`

### 3. Update Legacy Mappings
Add to `LEGACY_CURRENCY_ID_MAP` for backward compatibility:
```typescript
'22': 'NEW', // Map old currency ID to new code
```

## 🎨 Context-Specific Styling

The system automatically chooses icon variants based on UI context:

| Context | Icon Variant | Use Case |
|---------|-------------|----------|
| `header` | `white` | Header components (dark background) |
| `card` | `white` | Summary cards (dark background) |
| `modal` | `white` | Modal components (dark background) |
| `table` | `black` | Table cells (light background) |
| `form` | `black` | Form inputs (light background) |
| `default` | `black` | Default fallback |

## 🔄 Migration from Old System

### Before (Hardcoded):
```tsx
// ❌ Old way - hardcoded everywhere
<span>$1,500.00</span>
<LKRCurrencyIcon variant="white" size={16} />
{formatCurrency(amount, 'USD')}
```

### After (Centralized):
```tsx
// ✅ New way - automatic currency detection
<CurrencyDisplay amount={1500} context="table" />
const { formatCurrency } = useCurrency();
{formatCurrency(amount)}
```

## 🚨 Important Notes

1. **Provider Setup**: Wrap your app with `CurrencyProvider`:
   ```tsx
   <CurrencyProvider>
     <App />
   </CurrencyProvider>
   ```

2. **Legacy Compatibility**: Old currency utilities still work but show deprecation warnings

3. **Icon Assets**: LKR currency uses custom SVG icons from `public/assets/currency/`

4. **Performance**: Currency context is optimized with React.memo and useMemo

## 🎯 Benefits

- **Single Point of Change**: Change currency in one place, reflects everywhere
- **Type Safety**: Full TypeScript support with proper interfaces
- **Accessibility**: Automatic ARIA labels and screen reader support
- **Performance**: Optimized with caching and memoization
- **Flexibility**: Easy to add new currencies or customize existing ones
- **Backward Compatible**: Existing code continues to work during migration

## 🔍 Troubleshooting

### Currency Not Changing?
1. Check if `CurrencyProvider` wraps your app
2. Verify `DEFAULT_CURRENCY_CODE` in `currencyConfig.ts`
3. Clear browser cache and restart dev server

### Icons Not Showing?
1. Check if SVG files exist in `public/assets/currency/`
2. Verify `hasCustomIcon: true` in currency config
3. Check console for loading errors

### Deprecation Warnings?
- Update components to use `useCurrency()` hook instead of old utilities
- Replace `LKRCurrencyIcon` with `CurrencyIcon` or `CurrencyDisplay`

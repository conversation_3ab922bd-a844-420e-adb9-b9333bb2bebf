# 🚀 Currency System Setup Guide

## Quick Setup (5 minutes)

### Step 1: Add CurrencyProvider to Your App

Wrap your main application with the `CurrencyProvider`:

```tsx
// app/layout.tsx or your main app component
import { CurrencyProvider } from '@/shared/contexts/CurrencyContext';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <CurrencyProvider>
          {children}
        </CurrencyProvider>
      </body>
    </html>
  );
}
```

### Step 2: Change Currency (Optional)

To change the default currency from LKR to another currency:

1. Open `shared/config/currencyConfig.ts`
2. Change line ~85:
   ```typescript
   export const DEFAULT_CURRENCY_CODE = 'USD'; // or 'EUR', 'GBP', etc.
   ```

### Step 3: Test the System

Create a test component to verify everything works:

```tsx
// components/CurrencyTest.tsx
import { CurrencyDisplay, CurrencyIcon } from '@/shared/UI/components';
import { useCurrency } from '@/shared/hooks/useCurrency';

export const CurrencyTest = () => {
  const { formatCurrency, getCode, getName } = useCurrency();

  return (
    <div className="p-4 border rounded">
      <h3>Currency System Test</h3>
      <p>Current Currency: {getName()} ({getCode()})</p>
      
      {/* Test currency display */}
      <div className="mt-2">
        <CurrencyDisplay amount={1500.50} context="default" />
      </div>
      
      {/* Test currency icon */}
      <div className="mt-2">
        <CurrencyIcon context="default" size={20} />
        <span className="ml-2">{formatCurrency(2500)}</span>
      </div>
    </div>
  );
};
```

## Migration Guide

### Replace Old Components

#### Before:
```tsx
// ❌ Old hardcoded approach
<span>$1,500.00</span>
<LKRCurrencyIcon variant="white" size={16} />
{formatCurrency(amount, 'USD')}
```

#### After:
```tsx
// ✅ New centralized approach
<CurrencyDisplay amount={1500} context="table" />
const { formatCurrency } = useCurrency();
{formatCurrency(amount)}
```

### Update Table Columns

#### Before:
```tsx
render: (value) => (
  <span>${value.toLocaleString()}</span>
)
```

#### After:
```tsx
render: (value) => (
  <CurrencyDisplay 
    amount={value} 
    context="table" 
    size={14}
    amountClassName="font-medium"
  />
)
```

### Update Summary Cards

#### Before:
```tsx
<span className="text-2xl font-bold">
  ${totalAmount.toLocaleString()}
</span>
```

#### After:
```tsx
<CurrencyDisplay 
  amount={totalAmount}
  context="card"
  size={20}
  amountClassName="text-2xl font-bold text-white"
/>
```

## Available Components

### CurrencyDisplay
Complete currency display with icon and amount:
```tsx
<CurrencyDisplay 
  amount={1500.50}
  context="table"        // 'header', 'card', 'table', 'form', 'modal'
  size={16}             // Icon size
  gap="sm"              // 'none', 'sm', 'md', 'lg'
  amountClassName="font-bold"
  hideZero={false}
/>
```

### CurrencyIcon
Just the currency icon:
```tsx
<CurrencyIcon 
  context="header"      // Auto-selects white for headers
  size={20}
  variant="red"         // Override auto-selection
/>
```

### useCurrency Hook
Access currency functions:
```tsx
const { 
  formatCurrency,       // Format with symbol (for text currencies)
  formatCurrencyAmount, // Format number only
  hasCustomIcon,        // Boolean: uses custom icons?
  getCode,             // Get currency code (LKR, USD, etc.)
  getName,             // Get currency name
  getIconVariant       // Get icon variant for context
} = useCurrency();
```

## Context-Specific Styling

The system automatically chooses appropriate styling:

| Context | Icon Color | Use Case |
|---------|------------|----------|
| `header` | White | Dark backgrounds |
| `card` | White | Summary cards |
| `modal` | White | Modal dialogs |
| `table` | Black | Light backgrounds |
| `form` | Black | Form inputs |

## Troubleshooting

### Currency Not Showing?
1. Ensure `CurrencyProvider` wraps your app
2. Check console for errors
3. Verify SVG files exist in `public/assets/currency/`

### Icons Not Loading?
1. Check network tab for 404 errors on SVG files
2. Verify file paths: `/assets/currency/chips_black.svg`
3. Clear browser cache

### TypeScript Errors?
1. Restart TypeScript server
2. Check imports are correct
3. Ensure all types are exported properly

## Performance Notes

- Currency context is optimized with React.memo
- SVG icons are cached automatically
- Formatting functions use memoization
- No re-renders unless currency actually changes

## Next Steps

1. ✅ Add CurrencyProvider to your app
2. ✅ Test with the CurrencyTest component
3. ✅ Gradually migrate existing components
4. ✅ Customize currency settings as needed
5. ✅ Add new currencies if required

The system is designed to be backward compatible, so you can migrate components gradually without breaking existing functionality.

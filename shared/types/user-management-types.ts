// User Management API Types

export interface UserListFilters {
  size: number;
  page: number;
  search?: string;
  username?: string;
  name?: string;
  phone?: string;
  userType?: number;
  searchById?: string;
  searchByNic?: string;
  sortBy?: string;
  order?: 'asc' | 'desc';
  status?: string;
  adminId?: number;
  phoneVerified?: string;
  emailVerified?: string;
  playersCommission?: string;
  kycDone?: string;
  profileActivate?: string;
  kycDocs?: 'uploaded' | 'notUploaded' | 'pending';
  playerId?: string;
  userID?: string;
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  balance?: {
    min: string;
    max: string;
  };
  lastLogin?: {
    startDate: string;
    endDate: string;
  };
  minAmount?: number;
  maxAmount?: number;
  vipLevels?: string;
  playerCategory?: string;
  tenantId?: number;
  affiliatedData?: 'all' | 'affiliated' | 'non-affiliated';
  searchPromoCode?: string;
  affiliateStatus?: string;
  currency?: number;
  playerType?: 'all_players' | 'active' | 'inactive';
  timeZone?: string;
  timeZoneName?: string;
}

export interface UserData {
  id: string;
  active: boolean;
  activeBonus: any;
  amount: number;
  avatarImage: string;
  bonus: any[];
  categoryType: number;
  category_title: string | null;
  city: string;
  clickid: string | null;
  countryCode: string;
  createdAt: string;
  currencyId: string;
  currencycode: string;
  dateofbirth: string;
  deletedAt: string | null;
  demo: boolean;
  disabledAt: string | null;
  disabledByType: string | null;
  email: string;
  emailVerified: boolean;
  firstName: string;
  forceResetPassword: boolean;
  gender: string | null;
  kycDone: boolean;
  lastLoginDate: string | null;
  lastName: string;
  lastdepositedamount: number;
  nationalId: string | null;
  nickName: string | null;
  nonCashAmount: number;
  parentId: string;
  parentType: string;
  parentemail: string;
  phone: string;
  phoneCode: string;
  phoneVerified: boolean;
  playerCategoryLevel: string | null;
  playerCommissionMaxPercentage: {
    id: string;
    key: string;
    value: string;
  };
  profileVerified: boolean;
  referralCode: string;
  selfExclusion: any;
  setting: any[];
  tenantId: string;
  totalWageredAmount: number;
  updatedAt: string;
  userDocuments: any[];
  userName: string;
  userType: number;
  vipLevel: number;
  wagerMultiplier: any;
  walletId: string;
  walletupdatedat: string;
  withdrawWagerAllowed: boolean;
  wyntaclickid: string | null;
  zipCode: string;
}

export interface UserListResponse {
  success: number;
  message: string;
  data: UserData[];
  count: number;
  totalPages?: number;
  currentPage?: number;
  perPage?: number;
}

export interface UserListError {
  success: number;
  message: string;
  errors?: Record<string, string[]>;
}

// User Details API Types
export interface UserSetting {
  id: string;
  key: string;
  value: string;
  description: string | null;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserDocument {
  id: string;
  type: string;
  status: string;
  url: string;
  uploadedAt: string;
}

export interface UserBonus {
  id: string;
  type: string;
  amount: number;
  status: string;
  expiresAt: string;
}

export interface PlayerCommissionMaxPercentage {
  id: string;
  key: string;
  value: string;
  createdAt: string;
  updatedAt: string;
  tenantId: string;
  description: string;
}

export interface UserDetailsData {
  id: string;
  tenantId: string;
  active: boolean;
  gender: string | null;
  parentType: string;
  parentId: string;
  userName: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  phoneCode: string;
  phoneVerified: boolean;
  emailVerified: boolean;
  createdAt: string;
  demo: boolean;
  updatedAt: string;
  selfExclusion: string;
  deletedAt: string | null;
  disabledAt: string | null;
  disabledByType: string | null;
  forceResetPassword: boolean;
  parentemail: string;
  walletid: string;
  walletupdatedat: string;
  currencyId: string;
  currencycode: string;
  profileVerified: boolean;
  avatarImage: string;
  withdrawWagerAllowed: boolean;
  categoryType: number;
  userType: number;
  referralCode: string;
  wagerMultiplier: number | null;
  playerCategoryLevel: number | null;
  category_title: string | null;
  clickid: string | null;
  wyntaclickid: string | null;
  amount: number;
  nonCashAmount: number;
  lastdepositedamount: number;
  setting: UserSetting[];
  userDocuments: UserDocument[];
  bonus: UserBonus[];
  activeBonus: UserBonus | null;
  playerCommissionMaxPercentage: PlayerCommissionMaxPercentage;
  totalWageredAmount: number;
}

export interface UserDetailsResponse {
  data: UserDetailsData;
  errors: string[];
  success: number;
  message: string;
  code: number;
}

// Financial Report API Types
export interface FinancialReportFilters {
  userId: string;
  actionType?: string[]; // URL encoded as %5B%5D (empty array)
  timePeriod?: Record<string, any>; // URL encoded as %7B%7D (empty object)
  dateTime?: Record<string, any>; // URL encoded as %7B%7D (empty object)
  timeZone?: string; // URL encoded like 'UTC%20+00:00'
  startDate?: string;
  endDate?: string;
  transactionType?: string[];
  status?: string[];
  minAmount?: number;
  maxAmount?: number;
  currency?: string;
}

// TurboStars Sportsbook API Types
export interface TurboStarsGameLaunchRequest {
  userName?: string; // Optional - backend handles user context when not provided
  providerName: 'TurboStars' | 'turbostars';
  gameName?: string;
}

export interface TurboStarsGameLaunchResponse {
  data: {
    host: string;
    cid: string;
    token: string;
    url: string;
  };
  errors: string[];
  success: number;
  message: string;
}

export interface TurboStarsError {
  success: number;
  message: string;
  errors?: Record<string, string[]>;
}

// WebSocket Bet Notification Types
export interface CashierTurboPlaceBetDetails {
  userId: number;        // user.id
  marketId: string;      // market identifier
  transactionId: string; // transaction identifier
  provider: 'turbostars'; // provider name
}

export interface WebSocketBetNotification {
  type: 'CASHIER_TURBO_BET_PLACED';
  data: CashierTurboPlaceBetDetails;
  timestamp: string;
}

export interface WebSocketConnectionConfig {
  url: string;
  channel: string;
  userId: string;
  token: string;
}

// Bet Details API Types
export interface BetDetailsRequest {
  transactionId: string;
  providerName: string;
}

export interface BetDetailsItem {
  id: string;
  marketName: string;
  rate: number;
  stake: number;
  status: string;
  selection?: string;
}

export interface BetDetailsData {
  provider: string;
  marketDetail: {
    marketId: string;
    marketName: string;
    marketStatus: string;
  };
  betDetails: {
    betId: string;
    betType: string;
    settlementStatus: string;
    betAmount: number;
    betQrCode: string;
    settlementAmount: number;
    createdDate: string;
  };
  betList: Array<{
    betId: string;
    betType: string;
    marketName: string;
    rate: number;
    stake: number;
  }>;
}

export interface BetDetailsResponse {
  success: number;
  message: string;
  data: BetDetailsData;
  errors?: string[];
}

export interface FinancialSummaryData {
  totalDeposits: number;
  totalWithdrawals: number;
  totalCancellations: number;
  netAmount: number;
  transactionCount: number;
  currency: string;
}

export interface FinancialTransactionData {
  id: string;
  type: 'deposit' | 'withdrawal' | 'cancellation' | 'bonus' | 'commission';
  amount: number;
  currency: string;
  status: 'completed' | 'pending' | 'cancelled' | 'failed';
  description?: string;
  transactionId: string;
  createdAt: string;
  updatedAt?: string;
  paymentMethod?: string;
  reference?: string;
}

export interface FinancialReportData {
  userId: string;
  summary: FinancialSummaryData;
  transactions: FinancialTransactionData[];
  totalAmount: number;
  tenantBaseCurrency: string;
  reportGeneratedAt: string;
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

export interface FinancialReportResponse {
  data: FinancialReportData;
  success: number;
  message: string;
  errors: string[];
  code: number;
}

// Wallet Transaction Interfaces
export interface WalletTransactionRequest {
  target_type: "User";
  target_email: string; // User ID
  transaction_amount: string;
  transaction_type: "deposit" | "withdraw";
  target_wallete_id: string; // Target wallet ID
  source_wallete_id: string; // Source wallet ID
  internal_comment?: string;
  utr_number?: string;
  transaction_comments?: string;
}

export interface WalletTransactionResponse {
  success: 1 | 0;
  message: string;
  record: {
    transaction_id: number;
  };
}

export interface WalletTransactionFormData {
  userId: string;
  userName?: string; // For display purposes
  walletId: string; // User's wallet ID from user details API
  transactionType: "deposit" | "withdraw";
  amount: string;
  internalComment: string;
  utrNumber: string;
  transactionComments: string;
}

export interface WalletTransactionModalProps {
  isOpen: boolean;
  onClose: () => void;
  transactionType?: "deposit" | "withdraw";
  preSelectedUser?: {
    id: string;
    userName: string;
    walletId: string;
  };
  onSuccess?: (_transactionId: number) => void;
}

export interface UserSelectionOption {
  value: string; // User ID
  label: string; // Display name
  walletId: string;
  email: string;
  active: boolean;
}

// Admin Wallet Interfaces
export interface AdminWalletRecord {
  id: number;
  amount: string;
  primary: boolean;
  currency_id: number;
  owner_type: "AdminUser";
  owner_id: number;
  created_at: string;
  updated_at: string;
  non_cash_amount: string;
  withdrawal_amount: string;
  one_time_bonus_amount: string;
  sports_freebet_amount: string;
  currency_name: string;
}

export interface AdminWalletResponse {
  success: 1 | 0;
  message: string;
  record: AdminWalletRecord[];
}

// Bet History / Transaction Interfaces - Updated to match actual API response
export interface BetHistoryTransaction {
  source_wallet_id: string | null;
  target_wallet_id: string;
  actionee_id: string;
  actionee_type: string;
  round_id: string | null;
  provider_id: number;
  transaction_id: string;
  debit_transaction_id: string;
  conversion_rate: number | null;
  transaction_type: string;
  comments: string | null;
  status: string;
  source_currency_id: string | null;
  target_currency_id: string;
  internal_tracking_id: string;
  seat_id: string | null;
  tenant_id: string;
  meta_data: {
    commissionPer: number;
    commissionAmount: number;
    otherCurrencyCommissionAmountData: {
      EUR: number;
      chips: number;
    };
  };
  amount: string;
  added_amount: string;
  deducted_amount: string;
  created_at: string;
  table_id: string | null;
  game_provider: string;
  from_wallet_fname: string | null;
  from_wallet_lname: string | null;
  from_wallet_uname: string | null;
  from_wallet_email: string | null;
  to_wallet_fname: string;
  to_wallet_lname: string;
  to_wallet_uname: string;
  to_wallet_email: string;
  action_by_fname: string;
  action_by_lname: string;
  action_by_uname: string;
  action_by_email: string;
  currency: string;
  initial_balance: string;
  ending_balance: string;
  revenue: string;
  bot_user_id: string | null;
  game_name: string | null;
  target_currency: string;
  maker_data: any;
  checker_data: any;
}

export interface BetHistoryFilters {
  size: number;
  page: number;
  gameProvider?: string;
  gameType?: string;
  search?: string;
  roundId?: string;
  id?: string;
  amount?: string;
  transactionId?: string;
  debitTransactionId?: string;
  utrNumber?: string;
  tenantId?: string;
  currencyId?: string;
  order?: 'asc' | 'desc';
  sortBy?: string;
  actionType?: string;
  actionCategory?: string;
  status?: string;
  timePeriod?: string;
  timeType?: string;
  timeZone?: string;
  timeZoneName?: string;
  lifetimeRecords?: boolean;
  playerId?: string; // Made optional for global queries
  dateTime?: string;
}

export interface BetHistoryApiResponse {
  data: {
    data: {
      result: {
        rows: BetHistoryTransaction[];
        count?: number;
        total_pages?: number;
        current_page?: number;
      };
    };
    message: string;
  };
  errors: string[];
  success: number;
}

export interface BetHistoryResponse {
  data: BetHistoryTransaction[];
  success: number;
  message: string;
  errors: string[];
  count?: number;
  totalPages?: number;
  currentPage?: number;
}

export interface FinancialReportError {
  success: number;
  message: string;
  errors?: Record<string, string[]>;
}

// Default filter values - only size and page by default
export const DEFAULT_USER_FILTERS: UserListFilters = {
  size: 10,
  page: 1
};

// Default bet history filters - Updated to match API requirements
export const DEFAULT_BET_HISTORY_FILTERS: Omit<BetHistoryFilters, 'playerId'> = {
  size: 25,
  page: 1,
  gameProvider: '',
  gameType: '',
  search: '',
  roundId: '',
  id: '',
  amount: '',
  transactionId: '',
  debitTransactionId: '',
  utrNumber: '',
  tenantId: '',
  currencyId: '',
  order: 'desc',
  sortBy: 'created_at',
  actionType: '',
  actionCategory: 'sports',
  timeType: 'today',
  timeZone: 'UTC +00:00',
  timeZoneName: 'UTC +00:00',
  lifetimeRecords: false
};

// Default financial report filters
export const DEFAULT_FINANCIAL_REPORT_FILTERS: Omit<FinancialReportFilters, 'userId'> = {
  actionType: [],
  timePeriod: {},
  dateTime: {},
  timeZone: 'UTC +00:00'
};

// Bet Win Report API Interfaces - New API Integration
export interface BetWinReportRequest {
  page: string;
  limit: string;
  startDate: string; // ISO format with time
  endDate: string; // ISO format with time
  transactionId?: string; // Optional filter for single bet
  playerId: string; // User-specific filter
}

export interface BetWinReportBetListItem {
  price: number;
  market: string;
  match: string;
}

export interface BetWinReportBetData {
  betId: string;
  status: string;
  betSlipId: string;
  createdAt: string; // ISO date string
  betAmount: string;
  winAmount: string;
  betList: BetWinReportBetListItem[];
  payoutStatus: number;
}

export interface BetWinReportResponse {
  data: BetWinReportBetData[];
  errors: string[];
  success: number; // 1 for success
  message: string;
  total: string; // Total count as string
}

export interface BetWinReportFilters {
  page: number;
  limit: number;
  startDate: string;
  endDate: string;
  transactionId?: string;
  playerId: string;
}

// Default bet win report filters
export const DEFAULT_BET_WIN_REPORT_FILTERS: Omit<BetWinReportFilters, 'playerId'> = {
  page: 1,
  limit: 25,
  startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16), // 30 days ago
  endDate: new Date().toISOString().slice(0, 16), // Now
  transactionId: undefined
};

// Helper function to build clean query object (removes undefined/empty values)
export const buildUserQuery = (filters: Partial<UserListFilters>): UserListFilters => {

  const cleanFilters: UserListFilters = {
    size: filters.size || DEFAULT_USER_FILTERS.size,
    page: filters.page || DEFAULT_USER_FILTERS.page
  };

  // Only add filters that have meaningful values
  // Combine username, name, phone searches into the main search field for API compatibility
  const searchTerms = [];
  if (filters.search && filters.search.trim()) {
    searchTerms.push(filters.search.trim());
  }
  if (filters.username && filters.username.trim()) {
    searchTerms.push(filters.username.trim());
  }
  if (filters.name && filters.name.trim()) {
    searchTerms.push(filters.name.trim());
  }
  if (filters.phone && filters.phone.trim()) {
    searchTerms.push(filters.phone.trim());
  }

  if (searchTerms.length > 0) {
    cleanFilters.search = searchTerms.join(' ');
  }

  if (filters.userType && filters.userType !== null) {
    cleanFilters.userType = filters.userType;
  }

  if (filters.searchById && filters.searchById.trim()) {
    cleanFilters.searchById = filters.searchById.trim();
  }

  if (filters.searchByNic && filters.searchByNic.trim()) {
    cleanFilters.searchByNic = filters.searchByNic.trim();
  }

  if (filters.sortBy) {
    cleanFilters.sortBy = filters.sortBy;
  }

  if (filters.order) {
    cleanFilters.order = filters.order;
  }

  if (filters.status && filters.status !== '') {
    cleanFilters.status = filters.status;
  }

  if (filters.adminId !== undefined && filters.adminId !== null) {
    cleanFilters.adminId = filters.adminId;
  }

  if (filters.phoneVerified && filters.phoneVerified !== '') {
    cleanFilters.phoneVerified = filters.phoneVerified;
  }

  if (filters.emailVerified && filters.emailVerified !== '') {
    cleanFilters.emailVerified = filters.emailVerified;
  }

  if (filters.playersCommission && filters.playersCommission.trim()) {
    cleanFilters.playersCommission = filters.playersCommission.trim();
  }

  if (filters.kycDone && filters.kycDone !== '') {
    cleanFilters.kycDone = filters.kycDone;
  }

  if (filters.profileActivate && filters.profileActivate !== '') {
    cleanFilters.profileActivate = filters.profileActivate;
  }

  if (filters.kycDocs) {
    cleanFilters.kycDocs = filters.kycDocs;
  }

  if (filters.dateRange && filters.dateRange.startDate && filters.dateRange.endDate) {
    cleanFilters.dateRange = filters.dateRange;
  }

  if (filters.balance && (filters.balance.min || filters.balance.max)) {
    cleanFilters.balance = filters.balance;
  }

  if (filters.lastLogin && filters.lastLogin.startDate && filters.lastLogin.endDate) {
    cleanFilters.lastLogin = filters.lastLogin;
  }

  if (filters.minAmount !== undefined && filters.minAmount !== null) {
    cleanFilters.minAmount = filters.minAmount;
  }

  if (filters.maxAmount !== undefined && filters.maxAmount !== null) {
    cleanFilters.maxAmount = filters.maxAmount;
  }

  if (filters.vipLevels && filters.vipLevels !== '') {
    cleanFilters.vipLevels = filters.vipLevels;
  }

  if (filters.playerCategory && filters.playerCategory.trim()) {
    cleanFilters.playerCategory = filters.playerCategory.trim();
  }

  if (filters.tenantId !== undefined && filters.tenantId !== null && filters.tenantId !== 0) {
    cleanFilters.tenantId = filters.tenantId;
  }

  if (filters.affiliatedData && filters.affiliatedData !== 'all') {
    cleanFilters.affiliatedData = filters.affiliatedData;
  }

  if (filters.searchPromoCode && filters.searchPromoCode.trim()) {
    cleanFilters.searchPromoCode = filters.searchPromoCode.trim();
  }

  if (filters.affiliateStatus && filters.affiliateStatus !== '') {
    cleanFilters.affiliateStatus = filters.affiliateStatus;
  }

  if (filters.currency !== undefined && filters.currency !== null) {
    cleanFilters.currency = filters.currency;
  }

  if (filters.playerType && filters.playerType !== 'all_players') {
    cleanFilters.playerType = filters.playerType;
  }

  if (filters.timeZone && filters.timeZone.trim()) {
    cleanFilters.timeZone = filters.timeZone.trim();
  }

  if (filters.timeZoneName && filters.timeZoneName.trim()) {
    cleanFilters.timeZoneName = filters.timeZoneName.trim();
  }

  // Handle playerId and userID from user search
  if (filters.playerId && filters.playerId.trim()) {
    cleanFilters.playerId = filters.playerId.trim();
  }

  if (filters.userID && filters.userID.trim()) {
    cleanFilters.userID = filters.userID.trim();
  }

  return cleanFilters;
};

// Login History API Types
export interface LoginHistoryData {
  city: string;
  region: string;
  countryName: string;
}

export interface LoginHistoryUser {
  userName: string;
}
export interface LoginHistoryRecord {
  id: string;
  tenantId: string;
  ip: string;
  network: string;
  userId: string;
  version: string;
  deviceId: string;
  deviceType: string;
  deviceModel: string;
  data: LoginHistoryData;
  signInCount: number;
  User: LoginHistoryUser;
  lastLoginDate: string;
  createdAt: string;
  updatedAt: string;
}

export interface LoginHistoryFilters {
  page: string;
  limit: string;
  startDate: string;
  endDate: string;
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  playerId?: string; // Optional: include only when viewing specific user's history
}

export interface LoginHistoryApiResponse {
  data: {
    rows: LoginHistoryRecord[];
    count: number;
  };
  errors: string[];
  success: number;
  message: string;
}

export interface LoginHistoryResponse {
  data: LoginHistoryRecord[];
  success: number;
  message: string;
  errors: string[];
  count: number;
  totalPages?: number;
  currentPage?: number;
}

// Default login history filters
export const DEFAULT_LOGIN_HISTORY_FILTERS: Omit<LoginHistoryFilters, 'playerId'> = {
  page: "1",
  limit: "10",
  startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16), // 7 days ago
  endDate: new Date().toISOString().slice(0, 16), // Current date
};

// shared/UI/components/currency/CurrencyIcon.tsx
// Smart Currency Icon Component
// Automatically uses the correct icon/symbol based on current currency context

"use client";

import React from 'react';
import LKRCurrencyIcon from '../icons/LKRCurrencyIcon';
import { getCurrentCurrencyConfig, getIconVariantForContext } from '@/shared/config/currencyConfig';

// Simple currency utilities using config directly (no context dependency)
const getCurrencyInfo = () => {
  const config = getCurrentCurrencyConfig();
  return {
    hasCustomIcon: config.hasCustomIcon,
    getIconVariant: (context: string) => getIconVariantForContext(context as any),
    getSymbol: () => config.symbol,
    getCode: () => config.code,
    getName: () => config.name,
    formatAmountOnly: (amount: number | string | null | undefined) => {
      if (amount === null || amount === undefined) return '0.00';
      const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
      if (isNaN(numAmount)) return '0.00';
      return new Intl.NumberFormat(config.locale, {
        minimumFractionDigits: config.decimalPlaces,
        maximumFractionDigits: config.decimalPlaces,
      }).format(numAmount);
    }
  };
};

export interface CurrencyIconProps {
  /** UI context for automatic icon variant selection */
  context?: 'header' | 'card' | 'modal' | 'table' | 'form' | 'default';
  /** Size of the icon in pixels */
  size?: number | string;
  /** Additional CSS classes */
  className?: string;
  /** Accessibility label (auto-generated if not provided) */
  'aria-label'?: string;
  /** Whether the icon is decorative (hidden from screen readers) */
  'aria-hidden'?: boolean;
  /** Override the automatic variant selection */
  variant?: 'black' | 'red' | 'white';
}

/**
 * Smart Currency Icon Component
 * 
 * Automatically displays the appropriate currency icon or symbol based on:
 * - Current currency configuration (from CurrencyContext)
 * - UI context (header, card, table, etc.)
 * - Currency type (custom icon vs text symbol)
 * 
 * Features:
 * - Automatic currency detection from context
 * - Context-aware icon variant selection
 * - Fallback to text symbols for non-icon currencies
 * - Full accessibility support
 * - Consistent sizing and styling
 * 
 * Usage:
 * ```tsx
 * // Basic usage - automatically detects currency and context
 * <CurrencyIcon context="table" size={16} />
 * 
 * // Header usage
 * <CurrencyIcon context="header" size={20} />
 * 
 * // Card usage
 * <CurrencyIcon context="card" size={18} />
 * 
 * // Manual variant override
 * <CurrencyIcon variant="red" size={16} />
 * ```
 */
const CurrencyIcon: React.FC<CurrencyIconProps> = ({
  context = 'default',
  size = 16,
  className = '',
  'aria-label': ariaLabel,
  'aria-hidden': ariaHidden = false,
  variant
}) => {
  // Use config-based currency info (no context dependency)
  const currencyInfo = getCurrencyInfo();
  const hasCustomIcon = currencyInfo.hasCustomIcon;
  const iconVariant = variant || currencyInfo.getIconVariant(context);
  const currencySymbol = currencyInfo.getSymbol();
  const currencyCode = currencyInfo.getCode();
  const currencyName = currencyInfo.getName();

  // Generate accessibility label if not provided
  const accessibilityLabel = ariaLabel || `${currencyName} currency`;

  // Convert size to pixels if it's a number
  const sizeValue = typeof size === 'number' ? `${size}px` : size;

  // For currencies with custom icons (like LKR)
  if (hasCustomIcon) {
    return (
      <LKRCurrencyIcon
        variant={iconVariant}
        size={size}
        className={className}
        aria-label={ariaHidden ? undefined : accessibilityLabel}
        aria-hidden={ariaHidden}
      />
    );
  }

  // For text-based currencies (USD, EUR, etc.)
  // Render as styled text symbol
  const getTextColorClass = () => {
    switch (iconVariant) {
      case 'white':
        return 'text-white';
      case 'red':
        return 'text-red-500';
      case 'black':
      default:
        return 'text-gray-800 dark:text-gray-200';
    }
  };

  return (
    <span
      className={`inline-flex items-center justify-center font-semibold ${getTextColorClass()} ${className}`}
      style={{
        fontSize: sizeValue,
        minWidth: sizeValue,
        minHeight: sizeValue
      }}
      aria-label={ariaHidden ? undefined : accessibilityLabel}
      aria-hidden={ariaHidden}
      title={ariaHidden ? undefined : `${currencyName} (${currencyCode})`}
    >
      {currencySymbol}
    </span>
  );
};

/**
 * Currency Display Component
 *
 * Combines currency icon and formatted amount in a single component
 * Perfect for most use cases where you need to show "icon + amount"
 *
 * Usage:
 * ```tsx
 * <CurrencyDisplay amount={1500.50} context="table" />
 * <CurrencyDisplay amount={balance} context="header" size={20} />
 * ```
 */
export interface CurrencyDisplayProps extends Omit<CurrencyIconProps, 'aria-label'> {
  /** Amount to display */
  amount: number | string | null | undefined;
  /** Gap between icon and amount */
  gap?: 'none' | 'sm' | 'md' | 'lg';
  /** Additional classes for the amount text */
  amountClassName?: string;
  /** Hide amount if zero */
  hideZero?: boolean;
  /** Custom formatting options */
  formatOptions?: {
    decimalPlaces?: number;
    locale?: string;
  };
}

export const CurrencyDisplay: React.FC<CurrencyDisplayProps> = ({
  amount,
  context = 'default',
  size = 16,
  className = '',
  gap = 'sm',
  amountClassName = '',
  hideZero = false,
  formatOptions: _formatOptions, // Mark as unused for now
  variant,
  'aria-hidden': ariaHidden = false
}) => {
  // Use config-based currency info (no context dependency)
  const currencyInfo = getCurrencyInfo();

  // Check if we should hide zero values
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  if (hideZero && (!numAmount || numAmount === 0)) {
    return null;
  }

  // Format the amount
  const formattedAmount = currencyInfo.formatAmountOnly(amount);

  // Don't render if amount is empty
  if (!formattedAmount) {
    return null;
  }

  // Get gap class
  const getGapClass = () => {
    switch (gap) {
      case 'none': return '';
      case 'sm': return 'gap-1';
      case 'md': return 'gap-2';
      case 'lg': return 'gap-3';
      default: return 'gap-1';
    }
  };

  return (
    <div
      className={`flex items-center ${getGapClass()} ${className}`}
      aria-hidden={ariaHidden}
    >
      <CurrencyIcon
        context={context}
        size={size}
        variant={variant}
        aria-hidden={true} // Hide from screen readers since parent handles accessibility
        className="flex-shrink-0"
      />
      <span className={`${amountClassName}`}>
        {formattedAmount}
      </span>
    </div>
  );
};

export default CurrencyIcon;

// shared/UI/components/currency/CurrencyTest.tsx
// Simple test component to verify currency system works

"use client";

import React from 'react';
import CurrencyIcon, { CurrencyDisplay } from './CurrencyIcon';
import { getCurrentCurrencyConfig } from '@/shared/config/currencyConfig';

/**
 * Currency Test Component
 * 
 * Simple component to test that the currency system works
 * without requiring CurrencyProvider context.
 * 
 * This component can be used to verify:
 * - Currency icons render correctly
 * - Currency display works with amounts
 * - Different contexts show appropriate variants
 * - Config-based system functions properly
 */
export const CurrencyTest: React.FC = () => {
  const config = getCurrentCurrencyConfig();
  const testAmounts = [100, 1500.50, 25000, 0];

  return (
    <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-2xl">
      <h2 className="text-xl font-bold mb-4 text-gray-900 dark:text-gray-100">
        🧪 Currency System Test
      </h2>

      {/* Current Configuration */}
      <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
          Current Configuration:
        </h3>
        <div className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
          <div>Currency: {config.name} ({config.code})</div>
          <div>Symbol: {config.symbol}</div>
          <div>Has Custom Icon: {config.hasCustomIcon ? 'Yes' : 'No'}</div>
          <div>Decimal Places: {config.decimalPlaces}</div>
        </div>
      </div>

      {/* Icon Tests */}
      <div className="mb-6">
        <h3 className="font-semibold mb-3 text-gray-900 dark:text-gray-100">
          Currency Icons:
        </h3>
        <div className="grid grid-cols-3 gap-4">
          <div className="p-3 bg-gray-100 dark:bg-gray-700 rounded text-center">
            <div className="text-xs text-gray-600 dark:text-gray-400 mb-2">Default</div>
            <CurrencyIcon context="default" size={24} />
          </div>
          <div className="p-3 bg-gray-900 rounded text-center">
            <div className="text-xs text-gray-300 mb-2">Header</div>
            <CurrencyIcon context="header" size={24} />
          </div>
          <div className="p-3 bg-red-100 dark:bg-red-900/20 rounded text-center">
            <div className="text-xs text-red-600 dark:text-red-400 mb-2">Red Variant</div>
            <CurrencyIcon variant="red" size={24} />
          </div>
        </div>
      </div>

      {/* Display Tests */}
      <div className="mb-6">
        <h3 className="font-semibold mb-3 text-gray-900 dark:text-gray-100">
          Currency Displays:
        </h3>
        <div className="space-y-3">
          {testAmounts.map((amount) => (
            <div key={amount} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Amount: {amount}
              </span>
              <CurrencyDisplay
                amount={amount}
                context="default"
                size={16}
                amountClassName="font-medium text-gray-900 dark:text-gray-100"
              />
            </div>
          ))}
        </div>
      </div>

      {/* Context Tests */}
      <div className="mb-6">
        <h3 className="font-semibold mb-3 text-gray-900 dark:text-gray-100">
          Context Examples:
        </h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="p-4 bg-gray-900 rounded">
            <div className="text-xs text-gray-400 mb-2">Header Context (Dark)</div>
            <CurrencyDisplay
              amount={5000}
              context="header"
              size={18}
              amountClassName="text-white font-semibold"
            />
          </div>
          <div className="p-4 bg-gray-100 dark:bg-gray-600 rounded">
            <div className="text-xs text-gray-600 dark:text-gray-300 mb-2">Table Context (Light)</div>
            <CurrencyDisplay
              amount={2500}
              context="table"
              size={16}
              amountClassName="text-gray-900 dark:text-gray-100 font-medium"
            />
          </div>
        </div>
      </div>

      {/* Color Inheritance Tests */}
      <div className="mb-6">
        <h3 className="font-semibold mb-3 text-gray-900 dark:text-gray-100">
          🎨 Color Inheritance Tests:
        </h3>
        <div className="space-y-3">
          <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded">
            <div className="text-xs text-gray-600 dark:text-gray-400 mb-2">Green Text Color</div>
            <div className="text-green-600 text-lg font-semibold">
              <CurrencyDisplay
                amount={1500}
                context="table"
                size={18}
                amountClassName="font-semibold"
              />
            </div>
          </div>
          <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded">
            <div className="text-xs text-gray-600 dark:text-gray-400 mb-2">Red Text Color</div>
            <div className="text-red-500 text-lg font-semibold">
              <CurrencyDisplay
                amount={2500}
                context="table"
                size={18}
                amountClassName="font-semibold"
              />
            </div>
          </div>
          <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded">
            <div className="text-xs text-gray-600 dark:text-gray-400 mb-2">Blue Text Color</div>
            <div className="text-blue-600 text-lg font-semibold">
              <CurrencyDisplay
                amount={3500}
                context="table"
                size={18}
                amountClassName="font-semibold"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Status */}
      <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
        <div className="text-sm text-green-800 dark:text-green-200">
          ✅ <strong>Currency system is working!</strong> Features:
          <ul className="mt-2 ml-4 list-disc space-y-1">
            <li>Sharp/crisp inline SVG icons with dynamic color inheritance</li>
            <li>Centralized currency configuration</li>
            <li>No hard-coded currency symbols</li>
            <li>Works without CurrencyProvider context</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default CurrencyTest;

"use client";

import Image from 'next/image';
import React from 'react';

export type LKRCurrencyVariant = 'black' | 'red' | 'white';

export interface LKRCurrencyIconProps {
  /** Color variant of the LKR currency icon */
  variant?: LKRCurrencyVariant;
  /** Size of the icon in pixels */
  size?: number | string;
  /** Additional CSS classes */
  className?: string;
  /** Accessibility label */
  'aria-label'?: string;
  /** Whether the icon is decorative (hidden from screen readers) */
  'aria-hidden'?: boolean;
}

/**
 * LKR Currency Icon Component
 * 
 * A reusable component that displays Sri Lankan Rupee (LKR) currency icons
 * using the SVG files from public/assets/currency/ folder.
 * 
 * Features:
 * - Three color variants: black, red, white
 * - Configurable size
 * - Accessibility support
 * - Consistent styling with existing icon system
 * - Fallback handling for missing icons
 * 
 * Usage:
 * ```tsx
 * // Basic usage
 * <LKRCurrencyIcon variant="black" size={20} />
 * 
 * // With custom styling
 * <LKRCurrencyIcon 
 *   variant="red" 
 *   size={24} 
 *   className="mr-2" 
 *   aria-label="Sri Lankan Rupee"
 * />
 * ```
 */
const LKRCurrencyIcon: React.FC<LKRCurrencyIconProps> = ({
  variant = 'black',
  size = 20,
  className = '',
  'aria-label': ariaLabel = 'Sri Lankan Rupee',
  'aria-hidden': ariaHidden = false,
}) => {
  // Convert size to pixels if it's a number
  const sizeValue = typeof size === 'number' ? `${size}px` : size;

  // Map variant to SVG file name
  const getIconPath = (variant: LKRCurrencyVariant): string => {
    return `/assets/currency/chips_${variant}.svg`;
  };

  // Fallback component for when SVG fails to load
  const _FallbackIcon: React.FC = () => (
    <div
      className={`inline-flex items-center justify-center bg-gray-500 text-white text-xs font-bold rounded-full ${className}`}
      style={{
        width: sizeValue,
        height: sizeValue,
        minWidth: sizeValue,
        minHeight: sizeValue
      }}
      aria-label={ariaLabel}
      aria-hidden={ariaHidden}
    >
      LKR
    </div>
  );

  return (
    <Image
      src={getIconPath(variant)}
      alt={ariaHidden ? '' : ariaLabel}
      aria-label={ariaHidden ? undefined : ariaLabel}
      aria-hidden={ariaHidden}
      className={`inline-block ${className}`}
      style={{
        width: sizeValue,
        height: sizeValue,
        minWidth: sizeValue,
        minHeight: sizeValue
      }}
      onError={(e) => {
        // Replace with fallback on error
        const target = e.target as HTMLImageElement;
        const parent = target.parentNode;
        if (parent) {
          const fallback = document.createElement('div');
          fallback.className = `inline-flex items-center justify-center bg-gray-500 text-white text-xs font-bold rounded-full ${className}`;
          fallback.style.width = sizeValue;
          fallback.style.height = sizeValue;
          fallback.style.minWidth = sizeValue;
          fallback.style.minHeight = sizeValue;
          fallback.textContent = 'LKR';
          fallback.setAttribute('aria-label', ariaLabel);
          if (ariaHidden) {
            fallback.setAttribute('aria-hidden', 'true');
          }
          parent.replaceChild(fallback, target);
        }
      }}
    />
  );
};

export default LKRCurrencyIcon;

// shared/utils/countApiUtils.ts - Utility functions for count API calls

import { useAuthStore } from '@/shared/stores/authStore';
import { checkAndHandle401 } from '@/shared/utils/globalApiErrorHandler';

/**
 * Response structure for count API calls
 */
export interface CountApiResponse {
  data: {
    data: {
      result: {
        count: string;
      };
      success: number;
    };
    message: string;
  };
  errors: any[];
}

/**
 * Generic function to fetch count from any GET API endpoint
 * Adds count=true parameter to the URL
 */
export const fetchCountFromApi = async (
  baseUrl: string,
  endpoint: string,
  additionalParams: Record<string, string> = {}
): Promise<number> => {
  const token = useAuthStore.getState().token;

  if (!token) {
    throw new Error('Authentication token is required');
  }

  // Build query parameters
  const params = new URLSearchParams({
    count: 'true',
    ...additionalParams
  });

  const url = `${baseUrl}${endpoint}?${params.toString()}`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
  });

  // Check for 401 errors and handle them globally
  await checkAndHandle401(response);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Failed to fetch count: ${response.status}`);
  }

  const apiResponse: CountApiResponse = await response.json();

  // Extract count from the nested response structure
  const count = apiResponse?.data?.data?.result?.count;

  if (count === undefined || count === null) {
    throw new Error('Count not found in API response');
  }

  // Convert string count to number
  return parseInt(count, 10) || 0;
};

/**
 * Fetch count for user management API
 */
export const fetchUserManagementCount = async (filters: Record<string, any> = {}): Promise<number> => {
  const baseUrl = process.env.NEXT_PUBLIC_STAGING_BACKEND_URL;

  if (!baseUrl) {
    throw new Error('Backend URL is not configured');
  }

  // Convert filters to query parameters, excluding pagination params
  const { page: _page, size: _size, ...countFilters } = filters;
  const params: Record<string, string> = {};

  Object.entries(countFilters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      params[key] = value.toString();
    }
  });

  return fetchCountFromApi(baseUrl, '/api/v2/admin/players', params);
};

/**
 * Fetch count for financial report API
 */
export const fetchFinancialReportCount = async (filters: Record<string, any> = {}): Promise<number> => {
  const baseUrl = process.env.NEXT_PUBLIC_STAGING_BACKEND_URL;

  if (!baseUrl) {
    throw new Error('Backend URL is not configured');
  }

  // Convert filters to query parameters, excluding pagination params
  const { page: _page, limit: _limit, ...countFilters } = filters;
  const params: Record<string, string> = {};

  Object.entries(countFilters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      params[key] = value.toString();
    }
  });

  return fetchCountFromApi(baseUrl, '/api/v2/admin/financial-report', params);
};

/**
 * Fetch count for cashier report API
 */
export const fetchCashierReportCount = async (filters: Record<string, any> = {}): Promise<number> => {
  const baseUrl = process.env.NEXT_PUBLIC_STAGING_BACKEND_URL;

  if (!baseUrl) {
    throw new Error('Backend URL is not configured');
  }

  // Convert filters to query parameters, excluding pagination params
  const { page: _page, limit: _limit, ...countFilters } = filters;
  const params: Record<string, string> = {};

  Object.entries(countFilters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      params[key] = value.toString();
    }
  });

  return fetchCountFromApi(baseUrl, '/api/v2/admin/cashier-report', params);
};

/**
 * Fetch count for login history API
 */
export const fetchLoginHistoryCount = async (filters: Record<string, any> = {}): Promise<number> => {
  const baseUrl = process.env.NEXT_PUBLIC_REPORTING_BACKEND_URL;

  if (!baseUrl) {
    throw new Error('Reporting backend URL is not configured');
  }

  // Convert filters to query parameters, excluding pagination params
  const { page: _page, limit: _limit, ...countFilters } = filters;
  const params: Record<string, string> = {};

  Object.entries(countFilters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      params[key] = value.toString();
    }
  });

  return fetchCountFromApi(baseUrl, '/api/v2/cashier/user-login-history', params);
};

/**
 * Calculate count for bet report (POST API - no count support)
 * Uses the response data length as count
 */
export const calculateBetReportCount = (responseData: any[]): number => {
  return Array.isArray(responseData) ? responseData.length : 0;
};

/**
 * Determine if pagination should be shown based on count and items per page
 */
export const shouldShowPagination = (totalCount: number, itemsPerPage: number): boolean => {
  return totalCount > itemsPerPage;
};

/**
 * Calculate total pages from count and items per page
 */
export const calculateTotalPages = (totalCount: number, itemsPerPage: number): number => {
  if (itemsPerPage <= 0) return 1;
  return Math.ceil(totalCount / itemsPerPage);
};

// app/(public)/layout.tsx
import React from 'react';
import type { Metadata } from 'next';

// Generate metadata for public pages
export const metadata: Metadata = {
  title: 'Bet Details - Print Betslip',
  description: 'View detailed information about your bet slip',
  robots: 'noindex, nofollow', // Since this is a private bet details page
};

interface PublicLayoutProps {
  children: React.ReactNode;
}

/**
 * Layout for public pages that don't require authentication
 * This layout provides a minimal structure without the main app layout
 */
export default function PublicLayout({ children }: PublicLayoutProps) {
  return (
    <div className="public-layout">
      {children}
    </div>
  );
}

// app/(components)/(content-layout)/financial-report/components/FinancialReportPageClient.tsx - Client-side component for financial report
"use client";

import fadeInStyles from '@/app/css/animations/fade-in.module.css';
import { useFinancialReport } from "@/shared/hooks/business/useFinancialReport";
import { useExportCsv } from "@/shared/hooks/business/useExportCsv";
import { FinancialReportFilters, FinancialReportResponse } from "@/shared/types/report-types";
import { Fragment, useCallback, useMemo } from "react";

// Import global components
import {
	GlobalDataTable,
	GlobalFilterSection,
	GlobalPageHeader
} from "@/shared/UI/components";
import { DEFAULT_FINANCIAL_REPORT_VISIBLE_FILTERS, FINANCIAL_REPORT_FILTERS } from "@/shared/config/financialReportFilters";

// Import table columns
import { getFinancialReportTableColumns } from "@/shared/components/tables/FinancialReportTableColumns";

// Import error and loading components
import { SpkErrorMessage } from "@/shared/UI/components";
import ReportSummaryCards, { ReportSummaryCardData } from '@/shared/UI/cards/ReportSummaryCards';

interface FinancialReportPageClientProps {
	initialFinancialReportResponse?: FinancialReportResponse | null;
	initialFilters?: FinancialReportFilters;
}

/**
 * Client-side component that handles all interactive functionality for financial report
 * Separated from the server component to maintain SSR SEO benefits
 * Uses custom hook for business logic separation and global components
 *
 * Features:
 * - Accepts server-side rendered initial data for performance
 * - Graceful fallback to client-side fetching
 * - Optimized re-rendering for pagination changes
 */
export function FinancialReportPageClient({
	initialFinancialReportResponse = null,
	initialFilters
}: FinancialReportPageClientProps = {}) {
	// Use custom hook for all business logic with initial data
	const {
		filters,
		financialReportResponse,
		isLoading,
		isError,
		error,
		isFetching,
		totalTransactions,
		totalAmount,
		totalDeposits,
		totalWithdrawals,
		handleFilterChange,
		handlePageChange,
		handleRefresh,
		isAuthenticated,
		hasHydrated
	} = useFinancialReport({
		initialFinancialReportResponse,
		initialFilters
	});

	// Export CSV functionality
	const { exportCsv, isExporting } = useExportCsv({
		module: 'financial_report',
		type: 'casino_transactions_db'
	});

	// Handle export CSV
	const handleExportCsv = useCallback(async () => {
		await exportCsv(filters);
	}, [exportCsv, filters]);

	// Memoize table columns for performance
	const columns = useMemo(() => getFinancialReportTableColumns(), []);

	// Handle items per page change - optimized with useCallback
	const handleItemsPerPageChange = useCallback((itemsPerPage: number) => {
		handleFilterChange({ size: itemsPerPage });
	}, [handleFilterChange]);

	// Handle page change with tracking
	const handlePageChangeWithTracking = useCallback((page: number) => {
		handlePageChange(page);
	}, [handlePageChange]);

	// Memoize summary cards data for performance
	const summaryCardsData: ReportSummaryCardData[] = useMemo(() => [
		{
			type: 'total-deposit',
			label: 'Total Deposit',
			value: totalDeposits
			// Removed hard-coded currency - will use centralized currency system
		},
		{
			type: 'total-withdraw',
			label: 'Total Withdraw',
			value: totalWithdrawals
			// Removed hard-coded currency - will use centralized currency system
		},
		{
			type: 'balance-report',
			label: 'Balance Report',
			value: totalAmount // Using totalAmount as balance - adjust based on actual data structure
			// Removed hard-coded currency - will use centralized currency system
		}
	], [totalDeposits, totalWithdrawals, totalAmount]);

	// Show loading skeleton while authentication is being checked
	if (!hasHydrated) {
		return (
			<div className="animate-pulse space-y-6">
				<div className="h-20 bg-[#404040] rounded mb-6"></div>
				<div className="h-40 bg-[#333333] rounded mb-6"></div>
				<div className="h-96 bg-[#333333] rounded"></div>
			</div>
		);
	}

	// Redirect to login if not authenticated (handled in hook)
	if (!isAuthenticated) {
		return null;
	}

	// Show loading state only for initial load, not for subsequent fetches
	if (isLoading && !financialReportResponse) {
		return (
			<div className="animate-pulse space-y-6">
				<div className="h-20 bg-[#404040] rounded mb-6"></div>
				<div className="h-40 bg-[#333333] rounded mb-6"></div>
				<div className="h-96 bg-[#333333] rounded"></div>
			</div>
		);
	}

	return (
		<Fragment>
			{/* Global Page Header */}
			<GlobalPageHeader
				title="Financial Report"
			/>

			{/* Main Content with Enhanced Design */}
			<div className={`grid grid-cols-12 gap-6 ${fadeInStyles.fadeIn}`}>
				{/* Global Filter Section */}
				<div className="xl:col-span-12 col-span-12">
					<GlobalFilterSection
						filters={filters}
						onFilterChange={handleFilterChange}
						isLoading={isLoading || isFetching}
						onExport={handleExportCsv}
						showExportButton={true}
						exportLabel={isExporting ? "Exporting..." : "Export CSV"}
						availableFilters={FINANCIAL_REPORT_FILTERS}
						defaultVisibleFilters={DEFAULT_FINANCIAL_REPORT_VISIBLE_FILTERS}
						title="Filters"
					/>
				</div>

				{/* Summary Cards */}
				<div className="xl:col-span-12 col-span-12">
					<ReportSummaryCards
						cards={summaryCardsData}
						backgroundType="general"
						isLoading={isLoading}
						gridColumns={3}
						height="130px"
					// className="mb-6"
					/>
				</div>

				{/* Enhanced Financial Report Table Section */}
				<div className="xl:col-span-12 col-span-12 transform transition-all duration-500 ease-in-out delay-100 rounded-[16px] overflow-visible relative">
					<div className="bg-filter p-[1rem] rounded-md">
						{isError ? (
							<SpkErrorMessage
								message={error?.message || "Failed to load financial report"}
								onRetry={handleRefresh}
								variant="alert"
								size="md"
							/>
						) : (
							<GlobalDataTable
								columns={columns}
								data={financialReportResponse?.data || []}
								isLoading={isLoading}
								showPagination={true}
								currentPage={filters.page}
								totalItems={totalTransactions}
								itemsPerPage={filters.size}
								totalPages={financialReportResponse?.totalPages}
								onPageChange={handlePageChangeWithTracking}
								onItemsPerPageChange={handleItemsPerPageChange}
								emptyText="No financial transactions found. Try adjusting your search filters."
								className="financial-report-table"
								minHeight="400px"
							/>
						)}
					</div>
				</div>
			</div>
		</Fragment>
	);
}
